<template>
  <view class="content border-base border">
    <up-button type="primary" :plain="true" text="镂空"></up-button>
  </view>
</template>

<script setup lang="ts"></script>

<style scoped>
  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx;
    min-height: 100vh;
    background-color: #f0f0f0;
    margin: 20rpx;
  }
</style>
