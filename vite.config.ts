import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import UnoCSS from '@unocss/vite'

// https://vitejs.dev/config/
export default defineConfig(async () => {
  return {
    plugins: [uni(), UnoCSS()], // 暂时移除 UnoCSS
    // css: {
    //   preprocessorOptions: {
    //     scss: {
    //       silenceDeprecations: ['legacy-js-api', 'color-functions', 'import'],
    //     },
    //   },
    // },
  }
})
